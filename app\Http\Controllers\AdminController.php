<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use App\Models\Departement;
use App\Models\Reception;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    public function AdminDashboard(){
        $totalDepartements = Departement::count();
        $totalReceptions = Reception::count();
        $totalEmployeesDAF = \DB::select('SELECT COUNT(*) AS total_empl FROM employee WHERE dep_id=20;')[0]->total_empl;
        
        // Récupérer les 4 réceptions les plus récentes avec leurs relations
        $recentReceptions = Reception::with('departement')
            ->orderBy('created_at', 'desc')
            ->take(4)
            ->get();
            
        return view('admin.index', compact('totalDepartements', 'totalReceptions', 'totalEmployeesDAF', 'recentReceptions'));
    }//End Methode

    public function AdminLogout(Request $request){
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/admin/login');
    }//End Methode

    public function AdminLogin(){
        return view('admin.admin_login');
    }//End Methode

    public function AdminProfile(){
        $id = Auth::user()->id;
        $profileData = User::find($id);
        return view('admin.admin_profile_view', compact('profileData'));
    }//End Methode

    public function AdminProfileStore(Request $request){
        $id = Auth::user()->id;
        $data = User::find($id);
        $data->name = $request->name;
        $data->last_name = $request->last_name;
        $data->email = $request->email;
        $data->phone = $request->phone;
        $data->address = $request->address;

        if ($request->file('photo')) {
            $file = $request->file('photo');
            @unlink(public_path('upload/admin_images/'.$data->photo));
            $filename = date('YmdHi').$file->getClientOriginalName();
            $file->move(public_path('upload/admin_images'),$filename);
            $data['photo'] = $filename;
        }
        $data->save();

        $notification = array(
            'message' => 'Le Profile de l\'Admin a été bien modifié',
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    }//End Methode

    public function AdminChangePassword(){
        $id = Auth::user()->id;
        $profileData = User::find($id);
        return view('admin.admin_change_password', compact('profileData'));
    }//End Method

    public function AdminPasswordUpdate(Request $request){
        //Validation
        $request->validate([
            'old_password' => 'required',
            'new_password' => 'required|confirmed'
        ]);

        if (!Hash::check($request->old_password, auth::user()->password)){
            $notification = array(
                'message' => 'L\'ancien Mot de Passe ne correspond pas.',
                'alert-type' => 'error'
            );
            return back()->with($notification);
        }

        //Modifier le Nouveau Mot de Passe
        User::whereId(auth()->user()->id)->update([
            'password' => Hash::make($request->new_password)
        ]);
        $notification = array(
            'message' => 'Mot de Passe modifié avec succès.',
            'alert-type' => 'success'
        );
    return back()->with($notification);
    }//EndMethod
}

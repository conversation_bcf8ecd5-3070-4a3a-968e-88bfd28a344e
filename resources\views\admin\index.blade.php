@extends('admin.admin_dashboard')
@section('styles')
<!-- Dashboard Modern CSS -->
<link rel="stylesheet" href="{{ asset('css/dashboard-modern.css') }}">
@endsection

@section('admin')
<div class="page-content dashboard-container">
    <!-- 1. En-tête et présentation générale -->
    <div class="welcome-banner animate-fade-in">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h2>Bienvenue, {{ Auth::user()->name }} !</h2>
                <p>Voici un aperçu de l'état actuel du parc automobile. Utilisez ce tableau de bord pour surveiller les statistiques clés, les tendances et les activités récentes.</p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="filter-container justify-content-lg-end">
                    <select class="form-select form-select-sm filter-select" id="dateRangeFilter">
                        <option value="this-week">Cette semaine</option>
                        <option value="this-month" selected>Ce mois</option>
                        <option value="last-month"><PERSON><PERSON> dernier</option>
                        <option value="last-year">Année dernière</option>
                    </select>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-light dropdown-toggle export-btn" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class='bx bx-export'></i> Exporter
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                            <li><a class="dropdown-item" href="javascript:exportData('pdf')"><i class='bx bxs-file-pdf me-2'></i>PDF</a></li>
                            <li><a class="dropdown-item" href="javascript:exportData('excel')"><i class='bx bxs-file-excel me-2'></i>Excel</a></li>
                            <li><a class="dropdown-item" href="javascript:exportData('csv')"><i class='bx bxs-file-csv me-2'></i>CSV</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2. Section des statistiques clés -->
    <div class="row row-cols-1 row-cols-md-2 row-cols-xl-4 g-4 mb-4">
        <!-- Carte statistique - Total des véhicules -->
        <div class="col animate-fade-in delay-1">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-vehicles">
                        <i class='bx bx-car'></i>
                    </div>
                    <h6 class="card-title">TOTAL DES VÉHICULES</h6>
                    <h3 class="stat-value">38</h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold">28 opérationnels</span> 
                        <span class="text-muted">(10 en maintenance)</span>
                        <span class="trend-indicator trend-up">+12%</span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Carte statistique - Réceptions récentes -->
        <div class="col animate-fade-in delay-2">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-receptions">
                        <i class='bx bx-archive-in'></i>
                    </div>
                    <h6 class="card-title">RÉCEPTIONS RÉCENTES</h6>
                    <h3 class="stat-value">{{ $totalReceptions }}</h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold">{{ $totalReceptions > 10 ? $totalReceptions - 10 : $totalReceptions }} terminées</span>
                        <span class="text-muted">({{ $totalReceptions > 10 ? 10 : 0 }} en attente)</span>
                        <span class="trend-indicator trend-up">+18%</span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Carte statistique - Directions actives -->
        <div class="col animate-fade-in delay-3">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-departments">
                        <i class='bx bxs-building'></i>
                    </div>
                    <h6 class="card-title">DIRECTIONS ACTIVES</h6>
                    <h3 class="stat-value">{{ $totalDepartements }}</h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold">Utilisation du parc</span>
                        <span class="text-muted">(85% d'efficacité)</span>
                        <span class="trend-indicator trend-up">+7%</span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Carte statistique - Personnel -->
        <div class="col animate-fade-in delay-4">
            <div class="stat-card">
                <div class="card-body">
                    <div class="stat-icon bg-personnel">
                        <i class='bx bx-group'></i>
                    </div>
                    <h6 class="card-title">PERSONNEL DE LA DAF</h6>
                    <h3 class="stat-value">{{ $totalEmployeesDAF }}</h3>
                    <p class="stat-desc">
                        <span class="text-success fw-bold">{{ round($totalEmployeesDAF * 0.3) }} chauffeurs</span>
                        <span class="text-muted">({{ $totalEmployeesDAF - round($totalEmployeesDAF * 0.3) }} utilisateurs)</span>
                        <span class="trend-indicator trend-up">+5%</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!-- 3. Section des graphiques interactifs -->
    <div class="row mb-4">
        <!-- Graphique principal - Évolution des réceptions -->
        <div class="col-lg-8 animate-fade-in delay-1">
            <div class="chart-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Évolution des réceptions</h5>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: rgba(30, 136, 229, 1)"></div>
                            <span>Réceptions</span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="receptionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Graphique circulaire - Répartition des véhicules -->
        <div class="col-lg-4 animate-fade-in delay-2">
            <div class="chart-card">
                <div class="card-header">
                    <h5>Répartition par type</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="vehicleTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Graphique à barres - Utilisation par département -->
        <div class="col-lg-8 animate-fade-in delay-3">
            <div class="chart-card">
                <div class="card-header">
                    <h5>Utilisation par direction</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="departmentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    <!-- 4. Section des activités récentes -->
    <div class="row mb-4">
        <div class="col-lg-8 animate-fade-in delay-1">
            <div class="activity-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Réceptions récentes</h5>
                    <a href="{{ route('liste_reception') }}" class="btn btn-sm btn-primary">Voir tout</a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table activity-table">
                            <thead>
                                <tr>
                                    <th>Référence</th>
                                    <th>Date</th>
                                    <th>Objet</th>
                                    <th>Direction</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($recentReceptions as $reception)
                                <tr>
                                    <td><strong>{{ $reception->reference_courier }}</strong></td>
                                    <td>{{ $reception->date_enregistrement }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <img src="{{ asset('backend/assets/images/document-icon.png') }}" alt="Document" class="vehicle-img me-2">
                                            <span>{{ Str::limit($reception->objet, 30) }}</span>
                                        </div>
                                    </td>
                                    <td>{{ $reception->departement ? $reception->departement->nom_departement : 'N/A' }}</td>
                                    <td>
                                        @if($reception->pv_reception)
                                            <span class="status-badge status-completed">Terminé</span>
                                        @else
                                            <span class="status-badge status-pending">En attente</span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('reception.details', $reception->id) }}" class="action-btn view" data-bs-toggle="tooltip" title="Voir détails"><i class='bx bx-show'></i></a>
                                        <a href="{{ route('editer_reception', $reception->id) }}" class="action-btn edit" data-bs-toggle="tooltip" title="Modifier"><i class='bx bx-edit'></i></a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center">Aucune réception trouvée</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 5. Widgets latéraux -->
        <div class="col-lg-4">
            <!-- Widget Calendrier -->
            <div class="side-widget animate-fade-in delay-2">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Événements à venir</h5>
                    <button class="btn btn-sm btn-outline-primary"><i class='bx bx-plus'></i></button>
                </div>
                <div class="card-body">
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">02</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Entretien Toyota Hilux</h6>
                            <p>Garage Central, 9h00</p>
                        </div>
                    </div>
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">05</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Contrôle technique Ford Ranger</h6>
                            <p>Centre Auto, 14h30</p>
                        </div>
                    </div>
                    <div class="calendar-date">
                        <div class="calendar-day">
                            <span class="day">10</span>
                            <span class="month">JUIN</span>
                        </div>
                        <div class="calendar-event">
                            <h6>Réception nouveau véhicule</h6>
                            <p>Concessionnaire, 10h00</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Widget Tâches prioritaires -->
            <div class="side-widget animate-fade-in delay-3">
                <div class="card-header">
                    <h5>Tâches prioritaires</h5>
                </div>
                <div class="card-body">
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task1">
                        </div>
                        <div class="task-content">
                            <h6>Valider les réceptions en attente</h6>
                            <p>3 réceptions à traiter</p>
                        </div>
                        <span class="task-priority priority-high">Urgent</span>
                    </div>
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task2">
                        </div>
                        <div class="task-content">
                            <h6>Planifier les entretiens du mois</h6>
                            <p>5 véhicules concernés</p>
                        </div>
                        <span class="task-priority priority-medium">Moyen</span>
                    </div>
                    <div class="task-item">
                        <div class="task-checkbox">
                            <input class="form-check-input" type="checkbox" id="task3">
                        </div>
                        <div class="task-content">
                            <h6>Mettre à jour les documents</h6>
                            <p>Assurances et cartes grises</p>
                        </div>
                        <span class="task-priority priority-low">Faible</span>
                    </div>
                </div>
            </div>
            
            <!-- Widget Alertes -->
            <div class="side-widget animate-fade-in delay-4">
                <div class="card-header">
                    <h5>Alertes</h5>
                </div>
                <div class="card-body">
                    <div class="alert-widget alert-warning">
                        <h6>Entretien à prévoir</h6>
                        <p>Toyota Hilux - 5000 km avant prochain entretien</p>
                    </div>
                    <div class="alert-widget alert-danger">
                        <h6>Assurance expirée</h6>
                        <p>Renault Duster - Expiration le 25/05/2025</p>
                    </div>
                    <div class="alert-widget alert-info">
                        <h6>Contrôle technique à prévoir</h6>
                        <p>Ford Ranger - À effectuer avant le 15/06/2025</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Toast pour les notifications -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
        <div id="updateToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <small>À l'instant</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Les données du tableau de bord ont été mises à jour avec succès.
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<!-- Dashboard Modern JS -->
<script src="{{ asset('js/dashboard-modern.js') }}"></script>
@endsection

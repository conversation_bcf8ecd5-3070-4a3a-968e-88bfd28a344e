<?php $__env->startSection('admin'); ?>

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }

    /* Cards et conteneurs */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header .header-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.8rem;
    }

    /* Fil d'ariane */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }

    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
    }

    .breadcrumb-item a i {
        margin-right: 0.5rem;
    }

    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }

    /* Boutons */
    .btn {
        border-radius: 10px;
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn i {
        font-size: 1.1rem;
    }

    .btn-primary {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(145deg, var(--secondary), var(--primary));
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    /* Animation d'entrée */
    .animate__fadeIn {
        animation-duration: 0.6s;
    }

    .animate__fadeInUp {
        animation-duration: 0.8s;
    }

    /* Avatar et éléments spécifiques aux détails */
    .avatar-placeholder {
        width: 96px;
        height: 96px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: bold;
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        margin-right: 1.5rem;
    }

    .employee-name {
        font-size: 1.75rem;
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 0.25rem;
    }

    .employee-position {
        font-size: 1.1rem;
        color: #6b7280;
    }

    .info-list {
        list-style: none;
        padding: 0;
    }

    .info-list li {
        padding: 1rem;
        border-bottom: 1px solid #f3f4f6;
        display: flex;
        align-items: flex-start;
    }

    .info-list li:last-child {
        border-bottom: none;
    }

    .info-list .info-label {
        min-width: 180px;
        font-weight: 500;
        color: var(--dark);
        display: flex;
        align-items: center;
    }

    .info-list .info-label i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
        color: var(--primary);
    }

    .info-list .info-value {
        flex: 1;
    }

    .card-section-title {
        font-weight: 600;
        color: var(--dark);
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f3f4f6;
    }

    /* Table de données */
    .data-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }

    .data-table th {
        background-color: #f8fafc;
        padding: 0.75rem 1rem;
        font-weight: 600;
        color: var(--dark);
        text-align: left;
        border-bottom: 2px solid #e5e7eb;
    }

    .data-table td {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f1f5f9;
        vertical-align: top;
    }

    .data-table tr:nth-child(even) {
        background-color: #fcfcfd;
    }

    .data-table tr:hover {
        background-color: #f9fafb;
    }

    .data-table th:first-child, 
    .data-table td:first-child {
        font-weight: 500;
    }
    
    .badge {
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Fil d'ariane amélioré -->
    <div class="page-breadcrumb d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3 d-flex align-items-center">
            <i class='bx bx-group me-2 text-primary fs-5'></i>
            <div>
                <h4 class="mb-0">Détails de l'Employé</h4>
                <p class="mb-0 text-secondary small">Informations détaillées du personnel</p>
            </div>
        </div>
        <div class="ms-auto">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>"><i class='bx bx-home-alt'></i> Tableau de bord</a></li>
                <li class="breadcrumb-item"><a href="<?php echo e(route('employees.index')); ?>"><i class='bx bx-group'></i> Personnel</a></li>
                <li class="breadcrumb-item active" aria-current="page">Détails</li>
            </ol>
        </div>
    </div>

    <div class="row">
        <!-- Section d'en-tête avec les informations principales -->
        <div class="col-12 mb-4">
            <div class="card animate__animated animate__fadeInUp">
                <div class="card-body">
                    <div class="d-md-flex align-items-center">
                        <div class="avatar-placeholder">
                            <?php echo e(strtoupper(substr($employee->first_name, 0, 1) . substr($employee->last_name, 0, 1))); ?>

                        </div>
                        <div>
                            <h2 class="employee-name"><?php echo e($employee->first_name); ?> <?php echo e($employee->last_name); ?></h2>
                            <p class="employee-position"><?php echo e($employee->position_held ?? 'Non défini'); ?></p>
                            
                            <div class="d-flex mt-3">
                                <a href="<?php echo e(route('employees.index')); ?>" class="btn btn-light me-2">
                                    <i class='bx bx-arrow-back'></i> Retour à la liste
                                </a>
                                <a href="/employees/edit/<?php echo e($employee->em_code); ?>" class="btn btn-success me-2">
                                    <i class='bx bx-edit-alt'></i> Modifier
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section d'informations principales -->
        <div class="col-md-6 mb-4">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.1s">
                <div class="card-header">
                    <i class='bx bx-user-circle header-icon'></i>
                    Informations Personnelles
                </div>
                <div class="card-body">
                    <ul class="info-list">
                        <li>
                            <div class="info-label"><i class='bx bx-id-card'></i> Matricule</div>
                            <div class="info-value"><?php echo e($employee->em_code ?? 'Non défini'); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-user'></i> Nom</div>
                            <div class="info-value"><?php echo e($employee->last_name); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-user'></i> Prénom</div>
                            <div class="info-value"><?php echo e($employee->first_name); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-envelope'></i> Email</div>
                            <div class="info-value"><?php echo e($employee->email ?? 'Non défini'); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-phone'></i> Téléphone</div>
                            <div class="info-value"><?php echo e($employee->telephone ?? 'Non défini'); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-map'></i> Adresse</div>
                            <div class="info-value"><?php echo e($employee->adresse ?? 'Non défini'); ?></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Section d'informations professionnelles -->
        <div class="col-md-6 mb-4">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.2s">
                <div class="card-header">
                    <i class='bx bx-briefcase header-icon'></i>
                    Informations Professionnelles
                </div>
                <div class="card-body">
                    <ul class="info-list">
                        <li>
                            <div class="info-label"><i class='bx bx-briefcase'></i> Poste</div>
                            <div class="info-value"><?php echo e($employee->position_held ?? 'Non défini'); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-building'></i> Direction</div>
                            <div class="info-value"><?php echo e($employee->departement ? $employee->departement->nom_departement : 'Non assigné'); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-calendar'></i> Date d'embauche</div>
                            <div class="info-value"><?php echo e($employee->em_joining_date ? \Carbon\Carbon::parse($employee->em_joining_date)->format('d/m/Y') : 'Non défini'); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-calendar-check'></i> Date création</div>
                            <div class="info-value"><?php echo e(\Carbon\Carbon::parse($employee->created_at)->format('d/m/Y à H:i')); ?></div>
                        </li>
                        <li>
                            <div class="info-label"><i class='bx bx-revision'></i> Dernière mise à jour</div>
                            <div class="info-value"><?php echo e(\Carbon\Carbon::parse($employee->updated_at)->format('d/m/Y à H:i')); ?></div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Section avec toutes les informations - Design amélioré -->
        <div class="col-12 mb-4">
            <div class="card animate__animated animate__fadeInUp" style="animation-delay: 0.3s">
                <div class="card-header">
                    <i class='bx bx-list-ul header-icon'></i>
                    Toutes les Informations
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php $columnCount = 0; ?>
                        <?php $__currentLoopData = $employeeColumns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $column): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $columnCount++; ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="info-card p-3 h-100" style="border-radius: 12px; border-left: 4px solid #4361ee; background-color: #f8fafc; box-shadow: 0 2px 10px rgba(0,0,0,0.03);">
                                    <div class="d-flex align-items-center mb-2">
                                        <?php
                                            // Définir une icône en fonction du type de colonne
                                            $icon = 'bx-data';
                                            if (strpos($column, 'id') !== false) $icon = 'bx-hash';
                                            if (strpos($column, 'name') !== false || strpos($column, 'first') !== false || strpos($column, 'last') !== false) $icon = 'bx-user';
                                            if (strpos($column, 'email') !== false) $icon = 'bx-envelope';
                                            if (strpos($column, 'phone') !== false || strpos($column, 'tel') !== false) $icon = 'bx-phone';
                                            if (strpos($column, 'date') !== false || strpos($column, 'created') !== false || strpos($column, 'updated') !== false) $icon = 'bx-calendar';
                                            if (strpos($column, 'address') !== false || strpos($column, 'adresse') !== false) $icon = 'bx-map';
                                            if (strpos($column, 'departement') !== false || strpos($column, 'dep_id') !== false) $icon = 'bx-building';
                                            if (strpos($column, 'corps') !== false) $icon = 'bx-group';
                                            if (strpos($column, 'position') !== false || strpos($column, 'poste') !== false) $icon = 'bx-briefcase';
                                            if (strpos($column, 'code') !== false) $icon = 'bx-barcode';
                                            if (strpos($column, 'status') !== false) $icon = 'bx-check-circle';
                                        ?>
                                        <i class='bx <?php echo e($icon); ?> text-primary me-2' style="font-size: 1.2rem;"></i>
                                        <h6 class="mb-0 text-capitalize fw-bold" style="color: #374151;"><?php echo e(str_replace('_', ' ', $column)); ?></h6>
                                    </div>
                                    <div class="info-value ps-4" style="border-left: 1px dashed #e5e7eb; margin-left: 10px;">
                                        <?php if(is_null($employee->$column)): ?>
                                            <span class="badge bg-light text-dark">Non défini</span>
                                        <?php elseif(($column == 'departement_id' || $column == 'dep_id') && $employee->departement): ?>
                                            <span class="text-success"><?php echo e($employee->departement->nom_departement); ?></span>
                                            <small class="text-muted d-block">(ID: <?php echo e($employee->$column); ?>)</small>
                                        <?php elseif($column == 'corps_id' && $employee->corps): ?>
                                            <span class="text-success"><?php echo e($employee->corps->corps_name); ?></span>
                                            <small class="text-muted d-block">(ID: <?php echo e($employee->$column); ?>)</small>
                                        <?php elseif($column == 'created_at' || $column == 'updated_at' || strpos($column, 'date') !== false && !is_null($employee->$column)): ?>
                                            <?php
                                                try {
                                                    $dateValue = \Carbon\Carbon::parse($employee->$column)->format('d/m/Y à H:i');
                                                    echo "<span class='text-primary'>$dateValue</span>";
                                                } catch (\Exception $e) {
                                                    echo $employee->$column;
                                                }
                                            ?>
                                        <?php elseif($column == 'em_code'): ?>
                                            <span class="fw-bold" style="color: #4361ee;"><?php echo e($employee->$column); ?></span>
                                        <?php elseif(is_numeric($employee->$column) && !strpos($column, 'id')): ?>
                                            <span class="text-success"><?php echo e($employee->$column); ?></span>
                                        <?php else: ?>
                                            <?php echo e($employee->$column); ?>

                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <?php if($columnCount % 3 == 0): ?>
                                <div class="w-100"></div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\GestionParkAutoFinal_ok\resources\views/employees/details.blade.php ENDPATH**/ ?>
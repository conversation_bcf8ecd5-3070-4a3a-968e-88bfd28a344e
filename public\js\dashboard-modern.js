// Dashboard Modern JS - Scripts personnalisés pour le dashboard

document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des graphiques
    initCharts();
    
    // Initialisation des tooltips
    initTooltips();
    
    // Initialisation des filtres de date
    initDateFilters();
    
    // Animation des éléments
    animateElements();
});

// Fonction pour initialiser les graphiques
function initCharts() {
    // Graphique des réceptions mensuelles
    if (document.getElementById('receptionChart')) {
        const ctx = document.getElementById('receptionChart').getContext('2d');
        
        const receptionChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
                datasets: [{
                    label: 'Réceptions',
                    data: [18, 25, 30, 22, 17, 29, 32, 27, 22, 30, 35, 40],
                    backgroundColor: 'rgba(30, 136, 229, 0.1)',
                    borderColor: 'rgba(30, 136, 229, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    pointBackgroundColor: 'rgba(30, 136, 229, 1)',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        padding: 10,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y + ' réceptions';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    }
                }
            }
        });
    }
    
    // Graphique de répartition des véhicules
    if (document.getElementById('vehicleTypeChart')) {
        const ctx = document.getElementById('vehicleTypeChart').getContext('2d');
        
        const vehicleTypeChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Voitures', 'Camions', 'Motos', 'Engins spéciaux'],
                datasets: [{
                    data: [45, 25, 15, 15],
                    backgroundColor: [
                        'rgba(30, 136, 229, 0.8)',
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(156, 39, 176, 0.8)'
                    ],
                    borderColor: '#fff',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        padding: 10,
                        displayColors: true,
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                },
                cutout: '70%'
            }
        });
    }
    
    // Graphique d'utilisation par département
    if (document.getElementById('departmentChart')) {
        const ctx = document.getElementById('departmentChart').getContext('2d');
        
        const departmentChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['DAF', 'DRH', 'DSI', 'DG', 'DCF'],
                datasets: [{
                    label: 'Véhicules utilisés',
                    data: [12, 8, 5, 7, 3],
                    backgroundColor: 'rgba(76, 175, 80, 0.8)',
                    borderColor: 'rgba(76, 175, 80, 1)',
                    borderWidth: 1,
                    borderRadius: 5,
                    barThickness: 20
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        padding: 10,
                        displayColors: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#6c757d'
                        }
                    }
                }
            }
        });
    }
}

// Fonction pour initialiser les tooltips
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Fonction pour initialiser les filtres de date
function initDateFilters() {
    const dateRangeSelect = document.getElementById('dateRangeFilter');
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', function() {
            // Simuler un chargement des données
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-white bg-opacity-75';
            loadingOverlay.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div>';
            
            const chartCards = document.querySelectorAll('.chart-card .card-body');
            chartCards.forEach(card => {
                card.style.position = 'relative';
                card.appendChild(loadingOverlay);
            });
            
            // Simuler un délai de chargement
            setTimeout(function() {
                document.querySelectorAll('.position-absolute').forEach(el => el.remove());
                
                // Mettre à jour les données (simulation)
                updateChartsData(dateRangeSelect.value);
            }, 800);
        });
    }
}

// Fonction pour mettre à jour les données des graphiques
function updateChartsData(dateRange) {
    // Cette fonction simulerait normalement une requête AJAX pour obtenir de nouvelles données
    // Ici, nous allons simplement simuler des données différentes selon la période
    
    // Mise à jour des statistiques
    const statValues = document.querySelectorAll('.stat-value');
    const trendIndicators = document.querySelectorAll('.trend-indicator');
    
    if (dateRange === 'this-month') {
        if (statValues[0]) statValues[0].textContent = '35';
        if (statValues[1]) statValues[1].textContent = '28';
        if (statValues[2]) statValues[2].textContent = '12';
        if (statValues[3]) statValues[3].textContent = '48';
        
        if (trendIndicators[0]) trendIndicators[0].textContent = '+8%';
        if (trendIndicators[1]) trendIndicators[1].textContent = '+15%';
        if (trendIndicators[2]) trendIndicators[2].textContent = '+5%';
        if (trendIndicators[3]) trendIndicators[3].textContent = '+3%';
    } else if (dateRange === 'last-month') {
        if (statValues[0]) statValues[0].textContent = '32';
        if (statValues[1]) statValues[1].textContent = '24';
        if (statValues[2]) statValues[2].textContent = '11';
        if (statValues[3]) statValues[3].textContent = '45';
        
        if (trendIndicators[0]) trendIndicators[0].textContent = '+5%';
        if (trendIndicators[1]) trendIndicators[1].textContent = '+10%';
        if (trendIndicators[2]) trendIndicators[2].textContent = '+2%';
        if (trendIndicators[3]) trendIndicators[3].textContent = '+1%';
    } else if (dateRange === 'last-year') {
        if (statValues[0]) statValues[0].textContent = '28';
        if (statValues[1]) statValues[1].textContent = '18';
        if (statValues[2]) statValues[2].textContent = '9';
        if (statValues[3]) statValues[3].textContent = '40';
        
        if (trendIndicators[0]) trendIndicators[0].textContent = '+25%';
        if (trendIndicators[1]) trendIndicators[1].textContent = '+55%';
        if (trendIndicators[2]) trendIndicators[2].textContent = '+33%';
        if (trendIndicators[3]) trendIndicators[3].textContent = '+20%';
    }
    
    // Notification de mise à jour
    const toast = new bootstrap.Toast(document.getElementById('updateToast'));
    toast.show();
}

// Fonction pour animer les éléments
function animateElements() {
    const elements = document.querySelectorAll('.animate-fade-in');
    elements.forEach(element => {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
    });
}

// Fonction pour exporter les données
function exportData(format) {
    // Simuler un téléchargement
    const toast = document.createElement('div');
    toast.className = 'position-fixed bottom-0 end-0 p-3';
    toast.style.zIndex = '5';
    toast.innerHTML = `
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Exportation</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Exportation en ${format.toUpperCase()} en cours...
            </div>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(function() {
        toast.remove();
        
        const successToast = document.createElement('div');
        successToast.className = 'position-fixed bottom-0 end-0 p-3';
        successToast.style.zIndex = '5';
        successToast.innerHTML = `
            <div class="toast show bg-success text-white" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <strong class="me-auto">Succès</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    Exportation en ${format.toUpperCase()} terminée avec succès !
                </div>
            </div>
        `;
        
        document.body.appendChild(successToast);
        
        setTimeout(function() {
            successToast.remove();
        }, 3000);
    }, 1500);
}

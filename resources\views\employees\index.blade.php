@extends('admin.admin_dashboard')
@section('admin')

<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

<style>
    :root {
        --primary: #4361ee;
        --primary-light: #4895ef;
        --secondary: #3f37c9;
        --accent: #4cc9f0;
        --success: #4ade80;
        --danger: #f87171;
        --warning: #fbbf24;
        --info: #60a5fa;
        --dark: #374151;
        --light: #f9fafb;
    }

    /* Cards et conteneurs */
    .card {
        border: none;
        border-radius: 16px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        padding: 1.2rem;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .card-header .header-icon {
        font-size: 1.4rem;
        margin-right: 0.5rem;
    }

    .card-body {
        padding: 1.8rem;
    }

    /* Fil d'ariane */
    .page-breadcrumb {
        background: linear-gradient(to right, #f9fafb, #f3f4f6);
        padding: 1rem 1.5rem;
        border-radius: 12px;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        border-left: 4px solid var(--primary);
    }

    .breadcrumb-item a {
        color: var(--primary);
        font-weight: 500;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .breadcrumb-item a:hover {
        color: var(--secondary);
        text-decoration: none;
    }

    .breadcrumb-item a i {
        margin-right: 0.5rem;
    }

    .breadcrumb-item.active {
        color: var(--dark);
        font-weight: 600;
    }

    /* Boutons */
    .btn {
        border-radius: 10px;
        padding: 0.6rem 1.2rem;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn i {
        font-size: 1.1rem;
    }

    .btn-primary {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        border: none;
    }

    .btn-primary:hover {
        background: linear-gradient(145deg, var(--secondary), var(--primary));
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .btn-success {
        background-color: var(--success);
        border: none;
    }

    .btn-danger {
        background-color: var(--danger);
        border: none;
    }

    .btn-sm {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }

    .btn-action {
        width: 36px;
        height: 36px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        margin-right: 0.4rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-action i {
        font-size: 1.2rem;
    }

    /* Tableau */
    .table {
        border-collapse: separate;
        border-spacing: 0 8px;
        margin-top: -8px;
    }

    .table th {
        background-color: #f8fafc;
        padding: 1rem;
        font-weight: 600;
        color: var(--dark);
        border: none;
        vertical-align: middle;
        white-space: nowrap;
    }

    .table td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid #f1f5f9;
        border-bottom: 1px solid #f1f5f9;
        color: #4b5563;
    }

    .table tr {
        transition: all 0.3s ease;
        margin-bottom: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    }

    .table tr:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        background-color: #f9fafb;
    }

    .table td:first-child {
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        border-left: 1px solid #f1f5f9;
    }

    .table td:last-child {
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        border-right: 1px solid #f1f5f9;
    }

    /* Badge de statut */
    .badge {
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        font-weight: 500;
        font-size: 0.85rem;
    }

    /* Couleurs des départements */
    .dept-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }

    /* Animation d'entrée */
    .animate__fadeIn {
        animation-duration: 0.6s;
    }

    .animate__fadeInUp {
        animation-duration: 0.8s;
    }

    /* Barre de recherche */
    .search-container {
        position: relative;
        display: flex;
    }

    .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        font-size: 1.2rem;
        z-index: 1;
    }

    .search-input {
        border: 1px solid #e5e7eb;
        border-radius: 10px 0 0 10px;
        padding: 0.6rem 0.6rem 0.6rem 2.5rem;
        width: 100%;
        transition: all 0.3s ease;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    }
    
    .search-button {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        border: none;
        border-radius: 0 10px 10px 0;
        padding: 0 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .search-button:hover {
        background: linear-gradient(145deg, var(--secondary), var(--primary));
        transform: translateY(-2px);
    }
    
    .reset-search {
        position: absolute;
        right: 4rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        font-size: 1.2rem;
        z-index: 1;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f4f6;
        border-radius: 50%;
        transition: all 0.3s ease;
    }
    
    .reset-search:hover {
        background-color: #e5e7eb;
        color: var(--danger);
    }
    
    /* Pagination */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        gap: 0.3rem;
    }
    
    .pagination .page-item .page-link {
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        padding: 0.5rem 0.8rem;
        color: var(--dark);
        background-color: #fff;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }
    
    .pagination .page-item .page-link:hover {
        background-color: #f3f4f6;
        color: var(--primary);
    }
    
    .pagination .page-item.active .page-link {
        background: linear-gradient(145deg, var(--primary), var(--secondary));
        color: white;
        border-color: transparent;
    }
    
    .pagination .page-item.disabled .page-link {
        color: #9ca3af;
        cursor: not-allowed;
    }

    /* Compteur d'entrées */
    .entries-info {
        font-size: 0.9rem;
        color: #6b7280;
    }

    /* Ajustements responsive */
    @media (max-width: 768px) {
        .card-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .card-header > div:last-child {
            margin-top: 0.5rem;
        }

        .entries-info {
            margin-top: 1rem;
            text-align: left;
        }
    }

    /* Style pour les alertes */
    .alert {
        border-radius: 12px;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .alert-success {
        background-color: rgba(74, 222, 128, 0.15);
        color: #166534;
        border-left: 4px solid var(--success);
    }

    .alert-danger {
        background-color: rgba(248, 113, 113, 0.15);
        color: #b91c1c;
        border-left: 4px solid var(--danger);
    }
</style>

<div class="page-content animate__animated animate__fadeIn">
    <!-- Fil d'ariane amélioré -->
    <div class="page-breadcrumb d-sm-flex align-items-center mb-4">
        <div class="breadcrumb-title pe-3 d-flex align-items-center">
            <i class='bx bx-group me-2 text-primary fs-5'></i>
            <div>
                <h4 class="mb-0">Personnel</h4>
                <p class="mb-0 text-secondary small">Gestion du personnel</p>
            </div>
        </div>
        <div class="ms-auto">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class='bx bx-home-alt'></i> Tableau de bord</a></li>
                <li class="breadcrumb-item active" aria-current="page">Liste du Personnel</li>
            </ol>
        </div>
        <div class="ms-auto">
            <a href="{{ route('employees.create') }}" class="btn btn-primary animate__animated animate__pulse animate__infinite animate__slow">
                <i class='bx bx-plus-circle'></i> Ajouter un Personnel
            </a>
        </div>
    </div>

    <!-- Carte principale des employés -->
    <div class="card animate__animated animate__fadeInUp">
        <div class="card-header">
            <div>
                <i class='bx bx-list-ul header-icon'></i>
                Liste du Personnel
            </div>
            <div class="d-flex align-items-center">
                <a href="{{ route('employees.deleted') }}" class="btn btn-light btn-sm me-2">
                    <i class='bx bx-user-x'></i> Personnel désactivé
                </a>
                <span class="badge bg-light text-dark">
                    Total: {{ $employees->total() }}
                </span>
            </div>
        </div>
        <div class="card-body">
            <!-- Barre de recherche avec formulaire -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <form action="{{ route('employees.index') }}" method="GET">
                        <div class="search-container">
                            <i class='bx bx-search search-icon'></i>
                            <input type="text" name="search" class="search-input" placeholder="Rechercher un membre du personnel..." value="{{ request('search') }}">
                            <button type="submit" class="search-button">
                                <i class='bx bx-search'></i>
                            </button>
                            @if(request('search'))
                                <a href="{{ route('employees.index') }}" class="reset-search">
                                    <i class='bx bx-x'></i>
                                </a>
                            @endif
                        </div>
                    </form>
                </div>
                <div class="col-md-6 d-flex justify-content-md-end align-items-center">
                    <div class="entries-info">
                        Affichage de <span class="fw-bold">{{ $employees->firstItem() ?? 0 }}</span> à <span class="fw-bold">{{ $employees->lastItem() ?? 0 }}</span> sur <span class="fw-bold">{{ $employees->total() }}</span> membres du personnel
                    </div>
                </div>
            </div>

            <!-- Tableau amélioré - Version allégée -->
            <div class="table-responsive">
                <table id="employeesTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50px">
                                <i class='bx bx-hash me-1 text-primary'></i> N° Mle
                            </th>
                            <th>
                                <i class='bx bx-user me-1 text-primary'></i> Nom et Prénom
                            </th>
                            <th>
                                <i class='bx bx-briefcase me-1 text-primary'></i> Poste
                            </th>
                            <th>
                                <i class='bx bx-building me-1 text-primary'></i> Direction
                            </th>
                            <th width="120px" class="text-center">
                                <i class='bx bx-cog me-1 text-primary'></i> Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($employees as $key => $employee)
                        <tr style="animation-delay: {{ $key * 0.05 }}s">
                            <td class="fw-bold">
                                {{ $employee->em_code ?? 'N/A' }}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="dept-color" style="background-color: {{ '#' . substr(md5($employee->first_name), 0, 6) }}"></span>
                                    <span class="fw-medium">{{ $employee->first_name }} {{ $employee->last_name }}</span>
                                </div>
                            </td>
                            <td>
                                {{ $employee->position_held ?? 'Non défini' }}
                            </td>
                            <td>
                                {{ $employee->departement ? $employee->departement->nom_departement : 'Non assigné' }}
                            </td>
                            <td class="text-center">
                                <a href="/employees/details/{{ $employee->em_code }}" title="Détails" class="btn btn-sm btn-info btn-action">
                                    <i class='bx bx-show'></i>
                                </a>
                                <a href="/employees/edit/{{ $employee->em_code }}" title="Modifier" class="btn btn-sm btn-success btn-action" data-bs-toggle="tooltip">
                                    <i class='bx bx-edit-alt'></i>
                                </a>
                                <button type="button" title="Supprimer" class="btn btn-sm btn-danger btn-action delete-btn" data-id="{{ $employee->em_code }}" data-name="{{ $employee->first_name }} {{ $employee->last_name }}">
                                    <i class='bx bx-trash-alt'></i>
                                </button>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $employees->links('pagination::bootstrap-5') }}
            </div>

            <!-- Message si aucun employé -->
            @if($employees->count() == 0)
            <div class="text-center py-5">
                <i class='bx bx-folder-open text-muted' style="font-size: 4rem"></i>
                <p class="mt-3 text-muted">Aucun membre du personnel n'a été trouvé</p>
                <a href="{{ route('employees.create') }}" class="btn btn-primary mt-2">
                    <i class='bx bx-plus-circle'></i> Ajouter un Personnel
                </a>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Arrêter l'animation infinie après quelques secondes
        setTimeout(function() {
            document.querySelector('.animate__infinite').classList.remove('animate__infinite');
        }, 3000);
        
        // Le code de recherche côté client a été supprimé
        // car nous utilisons maintenant une recherche côté serveur
        
        // Effet hover sur les boutons d'action
        const actionButtons = document.querySelectorAll('.btn-action');
        actionButtons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.querySelector('i').classList.add('animate__animated', 'animate__heartBeat');
            });
            
            button.addEventListener('mouseleave', function() {
                this.querySelector('i').classList.remove('animate__animated', 'animate__heartBeat');
            });
        });
    });
</script>

<!-- Modal Détails de l'employé -->
<div class="modal fade" id="employeeDetailsModal" tabindex="-1" aria-labelledby="employeeDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="employeeDetailsModalLabel">
                    <i class='bx bx-user-pin me-2'></i>
                    Détails de l'employé
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4" id="employee-loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des données...</p>
                </div>
                
                <div id="employee-details" style="display: none;">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="d-flex align-items-center">
                                <div class="employee-avatar me-3">
                                    <div class="avatar-placeholder bg-primary text-white"></div>
                                </div>
                                <div>
                                    <h4 class="employee-name mb-1"></h4>
                                    <p class="employee-position text-muted mb-0"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-group mb-3">
                                <li class="list-group-item">
                                    <strong><i class='bx bx-id-card me-2 text-primary'></i> Matricule :</strong>
                                    <span class="employee-code float-end"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong><i class='bx bx-envelope me-2 text-primary'></i> Email :</strong>
                                    <span class="employee-email float-end"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong><i class='bx bx-phone me-2 text-primary'></i> Téléphone :</strong>
                                    <span class="employee-phone float-end"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong><i class='bx bx-building me-2 text-primary'></i> Direction :</strong>
                                    <span class="employee-department float-end"></span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-group mb-3">
                                <li class="list-group-item">
                                    <strong><i class='bx bx-calendar me-2 text-primary'></i> Date d'embauche :</strong>
                                    <span class="employee-hire-date float-end"></span>
                                </li>
                                <li class="list-group-item">
                                    <strong><i class='bx bx-map me-2 text-primary'></i> Adresse :</strong>
                                    <span class="employee-address float-end"></span>
                                </li>
                                <li class="list-group-item" id="other-info-container">
                                    <!-- Contiendra les autres colonnes de la table -->
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-light">
                                    <i class='bx bx-list-ul me-2'></i> Toutes les informations
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="allDetailsTable">
                                            <thead>
                                                <tr>
                                                    <th>Colonne</th>
                                                    <th>Valeur</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- Contiendra toutes les colonnes de la table -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class='bx bx-x-circle me-1'></i> Fermer
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Script pour charger les détails de l'employé -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser les avatars des employés
        const avatarPlaceholders = document.querySelectorAll('.avatar-placeholder');
        avatarPlaceholders.forEach(avatar => {
            const name = avatar.closest('.row').querySelector('.employee-name').textContent;
            const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
            avatar.textContent = initials || 'E';
        });
        
        // Gestionnaire pour le bouton de détails
        const detailButtons = document.querySelectorAll('.show-details');
        detailButtons.forEach(button => {
            button.addEventListener('click', function() {
                const employeeId = this.getAttribute('data-id');
                loadEmployeeDetails(employeeId);
            });
        });
        
        // Fonction pour charger les détails de l'employé
        function loadEmployeeDetails(employeeId) {
            // Afficher le chargement et masquer les détails
            document.getElementById('employee-loading').style.display = 'block';
            document.getElementById('employee-details').style.display = 'none';
            
            // Requête AJAX pour obtenir les détails via l'API
            fetch(`/api/employee/${employeeId}`)
                .then(response => response.json())
                .then(data => {
                    // Si la requête a échoué, afficher l'erreur
                    if (!data.success) {
                        document.getElementById('employee-loading').innerHTML = `
                            <div class="alert alert-danger">
                                <i class='bx bx-error-circle me-1'></i>
                                ${data.message}
                            </div>
                        `;
                        return;
                    }
                    
                    // Masquer le chargement et afficher les détails
                    document.getElementById('employee-loading').style.display = 'none';
                    document.getElementById('employee-details').style.display = 'block';
                    
                    // Remplir les informations de base
                    document.querySelector('.employee-name').textContent = `${data.employee.first_name} ${data.employee.last_name}`;
                    document.querySelector('.employee-position').textContent = data.employee.position_held || 'Non défini';
                    document.querySelector('.employee-code').textContent = data.employee.em_code || 'Non défini';
                    document.querySelector('.employee-email').textContent = data.employee.email || 'Non défini';
                    document.querySelector('.employee-phone').textContent = data.employee.telephone || 'Non défini';
                    document.querySelector('.employee-department').textContent = data.departement || 'Non assigné';
                    document.querySelector('.employee-hire-date').textContent = data.employee.date_embauche ? new Date(data.employee.date_embauche).toLocaleDateString() : 'Non défini';
                    document.querySelector('.employee-address').textContent = data.employee.adresse || 'Non défini';
                    
                    // Initialiser l'avatar avec les initiales
                    const initials = `${data.employee.first_name.charAt(0)}${data.employee.last_name.charAt(0)}`.toUpperCase();
                    document.querySelector('.avatar-placeholder').textContent = initials;
                    
                    // Remplir le tableau avec toutes les colonnes
                    const tableBody = document.querySelector('#allDetailsTable tbody');
                    tableBody.innerHTML = '';
                    
                    // Ajouter toutes les propriétés à la table
                    Object.entries(data.employee).forEach(([key, value]) => {
                        if (key !== 'departement') {
                            const row = document.createElement('tr');
                            
                            const cellKey = document.createElement('td');
                            cellKey.textContent = key;
                            row.appendChild(cellKey);
                            
                            const cellValue = document.createElement('td');
                            cellValue.textContent = value || 'Non défini';
                            row.appendChild(cellValue);
                            
                            tableBody.appendChild(row);
                        }
                    });
                    
                    // Ajouter le nom du département
                    const rowDept = document.createElement('tr');
                    const cellKeyDept = document.createElement('td');
                    cellKeyDept.textContent = 'departement';
                    rowDept.appendChild(cellKeyDept);
                    
                    const cellValueDept = document.createElement('td');
                    cellValueDept.textContent = data.departement;
                    rowDept.appendChild(cellValueDept);
                    
                    tableBody.appendChild(rowDept);
                })
                .catch(error => {
                    console.error('Erreur lors du chargement des détails:', error);
                    document.getElementById('employee-loading').innerHTML = `
                        <div class="alert alert-danger">
                            <i class='bx bx-error-circle me-1'></i>
                            Une erreur est survenue lors du chargement des détails.
                        </div>
                    `;
                });
        }
    });
</script>

<!-- Styles spécifiques pour la modal -->
<style>
    .avatar-placeholder {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .modal-header {
        background: linear-gradient(145deg, var(--primary), var(--secondary)) !important;
    }
    
    .employee-name {
        font-size: 1.4rem;
        color: var(--dark);
    }
    
    .list-group-item strong {
        color: var(--dark);
    }
    
    #allDetailsTable th {
        background-color: #f8f9fa;
    }
</style>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion de la suppression d'un employé
        const deleteBtns = document.querySelectorAll('.delete-btn');
        
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const employeeId = this.getAttribute('data-id');
                const employeeName = this.getAttribute('data-name');
                
                Swal.fire({
                    title: 'Désactiver cet employé?',
                    html: `Voulez-vous vraiment désactiver <strong>${employeeName}</strong> ?<br>Cet employé sera déplacé vers la liste du personnel désactivé.`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#f87171',
                    cancelButtonColor: '#6b7280',
                    confirmButtonText: 'Oui, désactiver',
                    cancelButtonText: 'Annuler',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Créer une requête AJAX pour désactiver l'employé
                        const xhr = new XMLHttpRequest();
                        xhr.open('POST', `/employees/destroy/${employeeId}`);
                        xhr.setRequestHeader('Content-Type', 'application/json');
                        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
                        xhr.responseType = 'json';
                        
                        // Ajouter le paramètre _method pour simuler une requête DELETE
                        const data = JSON.stringify({
                            _method: 'DELETE'
                        });
                        
                        xhr.onload = function() {
                            if (xhr.status === 200) {
                                Swal.fire({
                                    title: 'Désactivé!',
                                    text: `L'employé ${employeeName} a été désactivé avec succès.`,
                                    icon: 'success',
                                    confirmButtonColor: '#4361ee'
                                }).then(() => {
                                    // Recharger la page pour mettre à jour la liste
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Erreur!',
                                    text: xhr.response?.message || 'Une erreur est survenue lors de la désactivation.',
                                    icon: 'error',
                                    confirmButtonColor: '#4361ee'
                                });
                            }
                        };
                        
                        xhr.onerror = function() {
                            Swal.fire({
                                title: 'Erreur!',
                                text: 'Une erreur de connexion est survenue.',
                                icon: 'error',
                                confirmButtonColor: '#4361ee'
                            });
                        };
                        
                        xhr.send(data);
                    }
                });
            });
        });
    });
</script>

@endsection
